#!/usr/bin/env python3
"""
测试用例解析节点

负责解析测试用例描述，提取步骤信息
支持聚合模式下使用字符串解析拆分步骤
"""

import re
from typing import List

from loguru import logger

from src.domain.ui_task.mobile.repo.do.State import DeploymentState
from src.schema.action_types import ExecutionStatus


def add_step_numbers_if_needed(steps: List[str]) -> List[str]:
    """
    为步骤添加序号，如果步骤没有以数字开头的话

    Args:
        steps: 原始步骤列表

    Returns:
        添加了序号的步骤列表
    """
    numbered_steps = []

    for i, step in enumerate(steps, 1):
        # 使用正则表达式检查步骤是否以数字开头（支持多种格式）
        # 匹配格式：数字. 数字) 数字、 数字- 数字空格 等
        if re.match(r'^\s*\d+[\.\)\、\-\s]', step.strip()):
            # 如果已经有序号，直接使用原步骤
            numbered_steps.append(step)
        else:
            # 如果没有序号，添加序号
            numbered_steps.append(f"{i}. {step.strip()}")

    return numbered_steps


def parse_test_case_node(state: DeploymentState) -> DeploymentState:
    """
    Parse test case description into individual steps
    支持分步模式和聚合模式的步骤解析
    """
    task_id = state["task_id"]
    logger.info(f"[{task_id}] 📋 Parsing test case into steps...")

    # Get test case description
    test_description = state.get("test_case_description", "")
    if not test_description:
        test_description = state.get("task", "")

    # 检查验证模式
    verification_mode = state.get("verification_mode", "aggregation")
    logger.info(f"[{task_id}] 🔍 Parsing mode: {verification_mode}")
    logger.info(f"[{task_id}] 🔍 Parsing test case steps from: {test_description}")

    # 根据验证模式选择不同的解析策略
    if verification_mode == "step_by_step":
        # 分步模式：任务已经被拆分为步骤，直接使用预定义的步骤列表
        steps = state.get("task_steps", [])
        if not steps:
            logger.warning(f"[{task_id}] ⚠️ Step-by-step mode but no predefined steps, using description as single step")
            steps = [test_description.strip()] if test_description.strip() else []
        logger.info(f"[{task_id}] ✓ Step-by-step mode using {len(steps)} predefined steps")
    else:
        # 聚合模式：使用GPT-4智能拆分步骤
        steps = parse_aggregation_steps_with_gpt4(test_description, task_id)

    # 为步骤添加序号（如果需要的话）
    if steps:
        original_steps_count = len(steps)
        steps = add_step_numbers_if_needed(steps)
        logger.info(f"[{task_id}] ✓ Added step numbers where needed for {original_steps_count} steps")

    if not steps:
        logger.error(f"[{task_id}] ❌ No steps found in test case description")
        state["completed"] = True
        state["execution_status"] = ExecutionStatus.FAILED
        return state

    logger.info(f"[{task_id}] ✓ Parsed {len(steps)} steps: {steps}")

    # Store parsed information
    state["task_steps"] = steps
    state["step_failed"] = False
    state["retry_count"] = 0
    state["max_retries"] = len(steps) * 2  # 根据步骤数量设置最大重试次数

    logger.info(f"[{task_id}] ✓ Parsed {len(steps)} steps for execution, max_retries set to {state['max_retries']}")

    return state


def parse_aggregation_steps_with_gpt4(description: str, task_id: str) -> List[str]:
    """
    使用普通字符串解析拆分聚合模式的任务描述为独立步骤
    根据换行符拆分步骤

    Args:
        description: 任务描述文本
        task_id: 任务ID用于日志

    Returns:
        步骤描述列表
    """
    logger.info(f"[{task_id}] 📝 Using string parsing to split aggregation steps by newlines...")

    try:
        # 根据换行符拆分步骤
        lines = description.strip().split('\n')

        # 过滤空行并清理每行的空白字符
        steps = []
        for line in lines:
            cleaned_line = line.strip()
            if cleaned_line:  # 只保留非空行
                steps.append(cleaned_line)

        if steps:
            logger.info(f"[{task_id}] ✓ Successfully parsed {len(steps)} steps by newlines")
            for i, step in enumerate(steps, 1):
                logger.info(f"[{task_id}] Step {i}: {step}")
            return steps
        else:
            # 如果没有有效步骤，返回原始描述作为单个步骤
            logger.warning(f"[{task_id}] ⚠️ No valid steps found, using original description as single step")
            return [description.strip()]

    except Exception as e:
        logger.error(f"[{task_id}] ❌ Error in string parsing: {str(e)}")
        # 出错时回退到原始描述
        return [description.strip()]